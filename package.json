{"name": "test", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^24.1.0", "typescript": "^5.8.3", "vite": "^7.0.4"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "dependencies": {"@univerjs/find-replace": "^0.10.0", "@univerjs/preset-sheets-core": "^0.10.0", "@univerjs/preset-sheets-data-validation": "^0.10.0", "@univerjs/preset-sheets-filter": "^0.10.0", "@univerjs/preset-sheets-find-replace": "^0.10.0", "@univerjs/preset-sheets-note": "^0.10.0", "@univerjs/preset-sheets-sort": "^0.10.0", "@univerjs/presets": "^0.10.0"}}