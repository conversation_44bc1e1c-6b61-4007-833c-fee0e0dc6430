lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@univerjs/find-replace':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-core':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-data-validation':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-filter':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-find-replace':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-note':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-sort':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/presets':
        specifier: ^0.10.0
        version: 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
    devDependencies:
      '@types/node':
        specifier: ^24.1.0
        version: 24.1.0
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      vite:
        specifier: ^7.0.4
        version: 7.0.6(@types/node@24.1.0)

packages:

  '@babel/runtime@7.28.2':
    resolution: {integrity: sha512-KHp2IflsnGywDjBWDkR9iEqiWSpc8GIi0lgTT3mOElT0PP1tG26P4tmFI2YvAdzgq9RGyoHZQEIEdZy6Ec5xCA==}
    engines: {node: '>=6.9.0'}

  '@esbuild/aix-ppc64@0.25.8':
    resolution: {integrity: sha512-urAvrUedIqEiFR3FYSLTWQgLu5tb+m0qZw0NBEasUeo6wuqatkMDaRT+1uABiGXEu5vqgPd7FGE1BhsAIy9QVA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.8':
    resolution: {integrity: sha512-OD3p7LYzWpLhZEyATcTSJ67qB5D+20vbtr6vHlHWSQYhKtzUYrETuWThmzFpZtFsBIxRvhO07+UgVA9m0i/O1w==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.8':
    resolution: {integrity: sha512-RONsAvGCz5oWyePVnLdZY/HHwA++nxYWIX1atInlaW6SEkwq6XkP3+cb825EUcRs5Vss/lGh/2YxAb5xqc07Uw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.8':
    resolution: {integrity: sha512-yJAVPklM5+4+9dTeKwHOaA+LQkmrKFX96BM0A/2zQrbS6ENCmxc4OVoBs5dPkCCak2roAD+jKCdnmOqKszPkjA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.8':
    resolution: {integrity: sha512-Jw0mxgIaYX6R8ODrdkLLPwBqHTtYHJSmzzd+QeytSugzQ0Vg4c5rDky5VgkoowbZQahCbsv1rT1KW72MPIkevw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.8':
    resolution: {integrity: sha512-Vh2gLxxHnuoQ+GjPNvDSDRpoBCUzY4Pu0kBqMBDlK4fuWbKgGtmDIeEC081xi26PPjn+1tct+Bh8FjyLlw1Zlg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.8':
    resolution: {integrity: sha512-YPJ7hDQ9DnNe5vxOm6jaie9QsTwcKedPvizTVlqWG9GBSq+BuyWEDazlGaDTC5NGU4QJd666V0yqCBL2oWKPfA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.8':
    resolution: {integrity: sha512-MmaEXxQRdXNFsRN/KcIimLnSJrk2r5H8v+WVafRWz5xdSVmWLoITZQXcgehI2ZE6gioE6HirAEToM/RvFBeuhw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.8':
    resolution: {integrity: sha512-WIgg00ARWv/uYLU7lsuDK00d/hHSfES5BzdWAdAig1ioV5kaFNrtK8EqGcUBJhYqotlUByUKz5Qo6u8tt7iD/w==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.8':
    resolution: {integrity: sha512-FuzEP9BixzZohl1kLf76KEVOsxtIBFwCaLupVuk4eFVnOZfU+Wsn+x5Ryam7nILV2pkq2TqQM9EZPsOBuMC+kg==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.8':
    resolution: {integrity: sha512-A1D9YzRX1i+1AJZuFFUMP1E9fMaYY+GnSQil9Tlw05utlE86EKTUA7RjwHDkEitmLYiFsRd9HwKBPEftNdBfjg==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.8':
    resolution: {integrity: sha512-O7k1J/dwHkY1RMVvglFHl1HzutGEFFZ3kNiDMSOyUrB7WcoHGf96Sh+64nTRT26l3GMbCW01Ekh/ThKM5iI7hQ==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.8':
    resolution: {integrity: sha512-uv+dqfRazte3BzfMp8PAQXmdGHQt2oC/y2ovwpTteqrMx2lwaksiFZ/bdkXJC19ttTvNXBuWH53zy/aTj1FgGw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.8':
    resolution: {integrity: sha512-GyG0KcMi1GBavP5JgAkkstMGyMholMDybAf8wF5A70CALlDM2p/f7YFE7H92eDeH/VBtFJA5MT4nRPDGg4JuzQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.8':
    resolution: {integrity: sha512-rAqDYFv3yzMrq7GIcen3XP7TUEG/4LK86LUPMIz6RT8A6pRIDn0sDcvjudVZBiiTcZCY9y2SgYX2lgK3AF+1eg==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.8':
    resolution: {integrity: sha512-Xutvh6VjlbcHpsIIbwY8GVRbwoviWT19tFhgdA7DlenLGC/mbc3lBoVb7jxj9Z+eyGqvcnSyIltYUrkKzWqSvg==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.8':
    resolution: {integrity: sha512-ASFQhgY4ElXh3nDcOMTkQero4b1lgubskNlhIfJrsH5OKZXDpUAKBlNS0Kx81jwOBp+HCeZqmoJuihTv57/jvQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.8':
    resolution: {integrity: sha512-d1KfruIeohqAi6SA+gENMuObDbEjn22olAR7egqnkCD9DGBG0wsEARotkLgXDu6c4ncgWTZJtN5vcgxzWRMzcw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.8':
    resolution: {integrity: sha512-nVDCkrvx2ua+XQNyfrujIG38+YGyuy2Ru9kKVNyh5jAys6n+l44tTtToqHjino2My8VAY6Lw9H7RI73XFi66Cg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.8':
    resolution: {integrity: sha512-j8HgrDuSJFAujkivSMSfPQSAa5Fxbvk4rgNAS5i3K+r8s1X0p1uOO2Hl2xNsGFppOeHOLAVgYwDVlmxhq5h+SQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.8':
    resolution: {integrity: sha512-1h8MUAwa0VhNCDp6Af0HToI2TJFAn1uqT9Al6DJVzdIBAd21m/G0Yfc77KDM3uF3T/YaOgQq3qTJHPbTOInaIQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.8':
    resolution: {integrity: sha512-r2nVa5SIK9tSWd0kJd9HCffnDHKchTGikb//9c7HX+r+wHYCpQrSgxhlY6KWV1nFo1l4KFbsMlHk+L6fekLsUg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.25.8':
    resolution: {integrity: sha512-zUlaP2S12YhQ2UzUfcCuMDHQFJyKABkAjvO5YSndMiIkMimPmxA+BYSBikWgsRpvyxuRnow4nS5NPnf9fpv41w==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.8':
    resolution: {integrity: sha512-YEGFFWESlPva8hGL+zvj2z/SaK+pH0SwOM0Nc/d+rVnW7GSTFlLBGzZkuSU9kFIGIo8q9X3ucpZhu8PDN5A2sQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.8':
    resolution: {integrity: sha512-hiGgGC6KZ5LZz58OL/+qVVoZiuZlUYlYHNAmczOm7bs2oE1XriPFi5ZHHrS8ACpV5EjySrnoCKmcbQMN+ojnHg==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.8':
    resolution: {integrity: sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@flatten-js/interval-tree@1.1.3':
    resolution: {integrity: sha512-xhFWUBoHJFF77cJO1D6REjdgJEMRf2Y2Z+eKEPav8evGKcLSnj1ud5pLXQSbGuxF3VSvT1rWhMfVpXEKJLTL+A==}

  '@floating-ui/core@1.7.2':
    resolution: {integrity: sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==}

  '@floating-ui/dom@1.7.2':
    resolution: {integrity: sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==}

  '@floating-ui/react-dom@2.1.4':
    resolution: {integrity: sha512-JbbpPhp38UmXDDAu60RJmbeme37Jbgsm7NrHGgzYYFKmblzRUh6Pa641dII6LsjwF4XlScDrde2UAzDo/b9KPw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.10':
    resolution: {integrity: sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==}

  '@grpc/grpc-js@1.13.4':
    resolution: {integrity: sha512-GsFaMXCkMqkKIvwCQjCrwH+GHbPKBjhwo/8ZuUkWHqbI73Kky9I+pQltrlT0+MWpedCoosda53lgjYfyEPgxBg==}
    engines: {node: '>=12.10.0'}

  '@grpc/proto-loader@0.7.15':
    resolution: {integrity: sha512-tMXdRCfYVixjuFK+Hk0Q1s38gV9zDiDJfWL3h1rv4Qc39oILCu1TRTDt7+fGUI8K4G1Fj125Hx/ru3azECWTyQ==}
    engines: {node: '>=6'}
    hasBin: true

  '@js-sdsl/ordered-map@4.4.2':
    resolution: {integrity: sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==}

  '@noble/ed25519@2.3.0':
    resolution: {integrity: sha512-M7dvXL2B92/M7dw9+gzuydL8qn/jiqNHaoR3Q+cb1q1GHV7uwE17WCyFMG+Y+TZb5izcaXk5TdJRrDUxHXL78A==}

  '@noble/hashes@1.8.0':
    resolution: {integrity: sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==}
    engines: {node: ^14.21.3 || >=16}

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==}

  '@radix-ui/primitive@1.1.2':
    resolution: {integrity: sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==}

  '@radix-ui/react-arrow@1.1.7':
    resolution: {integrity: sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.7':
    resolution: {integrity: sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.2':
    resolution: {integrity: sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.2':
    resolution: {integrity: sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.14':
    resolution: {integrity: sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.1':
    resolution: {integrity: sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.10':
    resolution: {integrity: sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.15':
    resolution: {integrity: sha512-mIBnOjgwo9AH3FyKaSWoSu/dYj6VdhJ7frEPiGTeXCdUFHjl9h3mFh2wwhEtINOmYXWhdpf1rY2minFsmaNgVQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.2':
    resolution: {integrity: sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.7':
    resolution: {integrity: sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-hover-card@1.1.14':
    resolution: {integrity: sha512-CPYZ24Mhirm+g6D8jArmLzjYu4Eyg3TTUHswR26QgzXBHBe64BO/RHOJKzmF/Dxb4y4f9PKyJdwm/O/AhNkb+Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.1':
    resolution: {integrity: sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-menu@2.1.15':
    resolution: {integrity: sha512-tVlmA3Vb9n8SZSd+YSbuFR66l87Wiy4du+YE+0hzKQEANA+7cWKH1WgqcEX4pXqxUFQKrWQGHdvEfw00TjFiew==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popover@1.1.14':
    resolution: {integrity: sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.7':
    resolution: {integrity: sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.9':
    resolution: {integrity: sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.4':
    resolution: {integrity: sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.1.3':
    resolution: {integrity: sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.10':
    resolution: {integrity: sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.7':
    resolution: {integrity: sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.2.3':
    resolution: {integrity: sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-tooltip@1.2.7':
    resolution: {integrity: sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.1':
    resolution: {integrity: sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.2.2':
    resolution: {integrity: sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-effect-event@0.0.2':
    resolution: {integrity: sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.1':
    resolution: {integrity: sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.1':
    resolution: {integrity: sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.1':
    resolution: {integrity: sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.1':
    resolution: {integrity: sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.2.3':
    resolution: {integrity: sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.1':
    resolution: {integrity: sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==}

  '@rc-component/portal@1.1.2':
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rc-component/trigger@2.3.0':
    resolution: {integrity: sha512-iwaxZyzOuK0D7lS+0AQEtW52zUWxoGqTGkke3dRyb8pYiShmRpCjB/8TzPI4R6YySCH7Vm9BZj/31VPiiQTLBg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@rollup/rollup-android-arm-eabi@4.46.1':
    resolution: {integrity: sha512-oENme6QxtLCqjChRUUo3S6X8hjCXnWmJWnedD7VbGML5GUtaOtAyx+fEEXnBXVf0CBZApMQU0Idwi0FmyxzQhw==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.46.1':
    resolution: {integrity: sha512-OikvNT3qYTl9+4qQ9Bpn6+XHM+ogtFadRLuT2EXiFQMiNkXFLQfNVppi5o28wvYdHL2s3fM0D/MZJ8UkNFZWsw==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.46.1':
    resolution: {integrity: sha512-EFYNNGij2WllnzljQDQnlFTXzSJw87cpAs4TVBAWLdkvic5Uh5tISrIL6NRcxoh/b2EFBG/TK8hgRrGx94zD4A==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.46.1':
    resolution: {integrity: sha512-ZaNH06O1KeTug9WI2+GRBE5Ujt9kZw4a1+OIwnBHal92I8PxSsl5KpsrPvthRynkhMck4XPdvY0z26Cym/b7oA==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.46.1':
    resolution: {integrity: sha512-n4SLVebZP8uUlJ2r04+g2U/xFeiQlw09Me5UFqny8HGbARl503LNH5CqFTb5U5jNxTouhRjai6qPT0CR5c/Iig==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.46.1':
    resolution: {integrity: sha512-8vu9c02F16heTqpvo3yeiu7Vi1REDEC/yES/dIfq3tSXe6mLndiwvYr3AAvd1tMNUqE9yeGYa5w7PRbI5QUV+w==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.46.1':
    resolution: {integrity: sha512-K4ncpWl7sQuyp6rWiGUvb6Q18ba8mzM0rjWJ5JgYKlIXAau1db7hZnR0ldJvqKWWJDxqzSLwGUhA4jp+KqgDtQ==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.46.1':
    resolution: {integrity: sha512-YykPnXsjUjmXE6j6k2QBBGAn1YsJUix7pYaPLK3RVE0bQL2jfdbfykPxfF8AgBlqtYbfEnYHmLXNa6QETjdOjQ==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.46.1':
    resolution: {integrity: sha512-kKvqBGbZ8i9pCGW3a1FH3HNIVg49dXXTsChGFsHGXQaVJPLA4f/O+XmTxfklhccxdF5FefUn2hvkoGJH0ScWOA==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.46.1':
    resolution: {integrity: sha512-zzX5nTw1N1plmqC9RGC9vZHFuiM7ZP7oSWQGqpbmfjK7p947D518cVK1/MQudsBdcD84t6k70WNczJOct6+hdg==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.46.1':
    resolution: {integrity: sha512-O8CwgSBo6ewPpktFfSDgB6SJN9XDcPSvuwxfejiddbIC/hn9Tg6Ai0f0eYDf3XvB/+PIWzOQL+7+TZoB8p9Yuw==}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-ppc64-gnu@4.46.1':
    resolution: {integrity: sha512-JnCfFVEKeq6G3h3z8e60kAp8Rd7QVnWCtPm7cxx+5OtP80g/3nmPtfdCXbVl063e3KsRnGSKDHUQMydmzc/wBA==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.46.1':
    resolution: {integrity: sha512-dVxuDqS237eQXkbYzQQfdf/njgeNw6LZuVyEdUaWwRpKHhsLI+y4H/NJV8xJGU19vnOJCVwaBFgr936FHOnJsQ==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.46.1':
    resolution: {integrity: sha512-CvvgNl2hrZrTR9jXK1ye0Go0HQRT6ohQdDfWR47/KFKiLd5oN5T14jRdUVGF4tnsN8y9oSfMOqH6RuHh+ck8+w==}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.46.1':
    resolution: {integrity: sha512-x7ANt2VOg2565oGHJ6rIuuAon+A8sfe1IeUx25IKqi49OjSr/K3awoNqr9gCwGEJo9OuXlOn+H2p1VJKx1psxA==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.46.1':
    resolution: {integrity: sha512-9OADZYryz/7E8/qt0vnaHQgmia2Y0wrjSSn1V/uL+zw/i7NUhxbX4cHXdEQ7dnJgzYDS81d8+tf6nbIdRFZQoQ==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.46.1':
    resolution: {integrity: sha512-NuvSCbXEKY+NGWHyivzbjSVJi68Xfq1VnIvGmsuXs6TCtveeoDRKutI5vf2ntmNnVq64Q4zInet0UDQ+yMB6tA==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.46.1':
    resolution: {integrity: sha512-mWz+6FSRb82xuUMMV1X3NGiaPFqbLN9aIueHleTZCc46cJvwTlvIh7reQLk4p97dv0nddyewBhwzryBHH7wtPw==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.46.1':
    resolution: {integrity: sha512-7Thzy9TMXDw9AU4f4vsLNBxh7/VOKuXi73VH3d/kHGr0tZ3x/ewgL9uC7ojUKmH1/zvmZe2tLapYcZllk3SO8Q==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.46.1':
    resolution: {integrity: sha512-7GVB4luhFmGUNXXJhH2jJwZCFB3pIOixv2E3s17GQHBFUOQaISlt7aGcQgqvCaDSxTZJUzlK/QJ1FN8S94MrzQ==}
    cpu: [x64]
    os: [win32]

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/hoist-non-react-statics@3.3.7':
    resolution: {integrity: sha512-PQTyIulDkIDro8P+IHbKCsw7U2xxBYflVzW/FgWdCAePD9xGSidgA76/GeJ6lBKoblyhf9pBY763gbrN+1dI8g==}
    peerDependencies:
      '@types/react': '*'

  '@types/node@24.1.0':
    resolution: {integrity: sha512-ut5FthK5moxFKH2T1CUOC6ctR67rQRvvHdFLCD2Ql6KXmMuCrjsSsRI9UsLCm9M18BMwClv4pn327UvB7eeO1w==}

  '@types/react-redux@7.1.34':
    resolution: {integrity: sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==}

  '@types/react@19.1.9':
    resolution: {integrity: sha512-WmdoynAX8Stew/36uTSVMcLJJ1KRh6L3IZRx1PZ7qJtBqT3dYTgyDTx8H1qoRghErydW7xw9mSJ3wS//tCRpFA==}

  '@univerjs-pro/collaboration-client-ui@0.10.0':
    resolution: {integrity: sha512-GbegutmceYECcILvCMxikthAhrCoORsDYWXhRURO7e0FjBq7KXMRpW5nYUlPqes9/u8pwYtN/jrIITOKoCt3Rw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/collaboration-client@0.10.0':
    resolution: {integrity: sha512-ty7iIhZTEFkiaejQuQhCoTsPidetbEviryiWk5/o8wm/v78N7wWNwfXG3+GnyT7yW3+sJGsn472CMz/NbRDeVg==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/collaboration@0.10.0':
    resolution: {integrity: sha512-L3ehBNrd/A2fzCKPvgkqL32L/l+ANxxq7iN6SlyyrpYbrXcCY4kdzDYrUaEgD9rnKtp3oJl0EEgEYByiHcjaXA==}

  '@univerjs-pro/docs-exchange-client@0.10.0':
    resolution: {integrity: sha512-94oqxJB33uM5O+hDu9q3yVYKTWbXc2Z1YOdnrg8PURLPaeh4mBofOHhNpEzbHUltE32uq8BNwflLVrg5czAyBA==}

  '@univerjs-pro/docs-print@0.10.0':
    resolution: {integrity: sha512-b11TiesVuf120fOhT07psEKBo3NUAf+iXzYgYLhzCottAZwAxLhaR8mvzJaxk6VgD35FWINd50HW+ZhFI84Ssw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  '@univerjs-pro/edit-history-loader@0.10.0':
    resolution: {integrity: sha512-jJsxlL0JNFb9Bw7Z5IhBkjkT1pccWR2Stu6GtHfqC32vryUR5wx2wfm5UqD+teK+WIbsBxd3wgkHlAsjt66UoQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/edit-history-viewer@0.10.0':
    resolution: {integrity: sha512-3OnS3KZ4ar4pgOorQVI2BcxTzfUHe3f6l40YHYW5Sg08hcQ5ZUfmMpTzdOAR0aynF2kkMj69mtAdn/bdkLwUkA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/engine-chart@0.10.0':
    resolution: {integrity: sha512-Wygd9py9OnytRBKyU4k4MkiioSYdrS5rx4e/m2+mTzJFQB66RzZ7NA6wgn5g3zXBsCC0UtAlOiykHuledZ8aCQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/engine-formula@0.10.0':
    resolution: {integrity: sha512-ZuSi1v93+gomzzl6V0NmmL66ZuWtiGkjPo13y98jx9Kwzs6GE0657Nwd/BW96q15lnI+aUEx+RuOGcmh2rBg9g==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/engine-pivot@0.10.0':
    resolution: {integrity: sha512-fI9HMaP/WbxenQsXgRdupz9hOIn8FW3EDKKRSZWrasjpzoOjt/4ytcwgkvOPY4wcAKc0iW64R2mzIh9hjFy56A==}

  '@univerjs-pro/exchange-client@0.10.0':
    resolution: {integrity: sha512-xOTtOA+aPZmr4zXQsK8MB/IfhYwK65UgoJBly+3nmzMj/lrDXbCBfU6IAd8At5xYkEUErNLQrIOoghv5PPJdQA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/license@0.10.0':
    resolution: {integrity: sha512-s+g4fCrDPQAgIUAXGx5YkWrYisvHcP7F1t4F2dNizoL0ebdA+Y8cBbeqgx+tqvBLzCA7etYGVXwXDu1FbKfmmw==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/print@0.10.0':
    resolution: {integrity: sha512-kD7HwkQKcvyR2/j9O5jGGCkpzxuXrYQIlA+wk+TJKAejoDxFISOG9RgIrgeTG6SH/6XemSFz0r9qFwDVlk0nHQ==}

  '@univerjs-pro/sheets-chart-ui@0.10.0':
    resolution: {integrity: sha512-7R5Whb1og+T0CBI4iVAzA+yZGync0iM5ibLKjZD8QLE3idpCn8QnztG6vYQ9vLYdbpOHZhKqi3XuVaC8UtDUsw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-chart@0.10.0':
    resolution: {integrity: sha512-KBovbrjZodB7A4IAB4KzsGKP0loD2LHjrAz5kkni9jmmt/VdENseaZldIub+Cfj3xfy1GaK5gCpdlENBFSGHBA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-exchange-client@0.10.0':
    resolution: {integrity: sha512-uEHDDG7kCO/7u7EE88XSHidI6v6tNUqumjvOl+nF3xeVYrmRS0WCv9uwogqcAFLr9oqfa62B/WW6TO0vfh3E0Q==}

  '@univerjs-pro/sheets-pivot-ui@0.10.0':
    resolution: {integrity: sha512-qzL7Hqpv3toPWHGrWz++aO9TzgsS7OBGteeCS8WAY/fjisqQ8hPGNCefK3r0EWJdwujt4qLSoW4BGhp9exUMpQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-pivot@0.10.0':
    resolution: {integrity: sha512-J51v8zKOnFXpHqESdwDA7vkpob6aQXBdrebKK+dgcBI0KkKrN7CMWEZ6AznkZo5tcsfzU+F27glp3WYYGFZa+Q==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-print@0.10.0':
    resolution: {integrity: sha512-lP05HY1pCwGq1T1n0PYaCuSRskvTnqhC/nLWhWeil1C4GvkHYzKlg6GwHOvjFZol2MXU3y6XGz0vTP04/BALhw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-sparkline-ui@0.10.0':
    resolution: {integrity: sha512-nwaJu3HoiKXyeEJYKKv99iGAYy4haCwLjLU8tJR0s6USTo0Hzqyx0OU2Y0cij/YTX6nhHx7PumIFcWmSUJx3vg==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs-pro/sheets-sparkline@0.10.0':
    resolution: {integrity: sha512-WpGXgSxPqIkiBtYK0gVmgQpYOl1qA9mxc4ro+UH4RFzshhh9MD+4144aPM47BrTqDS3JTDeXXFHYKIzfOsK2Rw==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs-pro/thread-comment-datasource@0.10.0':
    resolution: {integrity: sha512-2FF6DLaKW9zEImSZimB97ZX6eapQRxxlH0PHuoUvnxPJb29kx2QVyrIT2L9ArWJaxUAvOS/HEpIzIS5sMEUmIg==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/core@0.10.0':
    resolution: {integrity: sha512-+3HdAZL/tEFxB5OFSTN8jiGNppsvPjtXEO/A7k20IagA+fFyT2GmSE44ET8y9T21WyqJ67njpo7enRhWP2sS1g==}
    peerDependencies:
      '@wendellhu/redi': 1.0.0
      rxjs: '>=7.0.0'

  '@univerjs/data-validation@0.10.0':
    resolution: {integrity: sha512-rSR07Y3f2pITBZ4oO7I2RtL95fYav/eKT3lKf7Ds204Iyg/4xLltJZE4JR5PoDVb5UzLC5kPoTtq+hwkOO/bQA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/design@0.10.0':
    resolution: {integrity: sha512-PPJ/Upm6Fq6IZmZvVXRDLq51bjMLc3WbqN8ZNT2C1lZMmiWE7OP6zjehSZZ+HnxFrtUYEeubiDxxRLUZZ3+OtA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  '@univerjs/docs-drawing-ui@0.10.0':
    resolution: {integrity: sha512-5YWJJ9KtO/KTUzBggKIAJUBZJ5SdF3yXtIoy6bkwtmLvnR+VvPAX73UkIND+d0D/4zWUHlLBqc5HTg4hr10nGw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/docs-drawing@0.10.0':
    resolution: {integrity: sha512-OMcIVAtndn3tkzEsH2TvidZDKRiN/ehAyD5TVH9GbYGfrWoB4lhDO+8gU3Hq6f5cMd6EyYOswwGR4va/aU2w/Q==}

  '@univerjs/docs-hyper-link-ui@0.10.0':
    resolution: {integrity: sha512-14PDn2+jNtCq1l/B7Zc7ScuEmkxudWFS7tXKpv18DAQSlIL85UTfiuDwz8nWnoTcQBR2G146R1HsxcbHAqMTLQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/docs-hyper-link@0.10.0':
    resolution: {integrity: sha512-sSrySaosdA2TOemmnkoGgHqeAQBv3mGLf9L13En/MzgCxB6fh9JIiGVURw46W4Dk5a3PEPgCFR54TSxpgErFTw==}

  '@univerjs/docs-thread-comment-ui@0.10.0':
    resolution: {integrity: sha512-f9kWig+sn64KBaGueH7GkPWSlYwBWJ/za7/9FgtR9MX5s/3iB+GrX60ZxiJEqBdvkETLHVAjZ8gN0To82TmGLw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/docs-ui@0.10.0':
    resolution: {integrity: sha512-Dl3IxJwn5oROjWNJ2FCqq/0l3nSnYJFmdqmdVHI3uUG/nLlXUmcBDIeillPpgV6HqtvqmX9p1w/7UDyuJ6L9AA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/docs@0.10.0':
    resolution: {integrity: sha512-MNj96d3NMwYkzNPlSysE37CRCYXnH1kxrxUwKHhQzA4d+oVKudVF2PQ6niC95qB1H0zunfh6k//Z1gIBGlD9RA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/drawing-ui@0.10.0':
    resolution: {integrity: sha512-BvSbvmN3xLJudnbPWqeoNWYXII9dTBsi05aq+ao3qZeZL6MXYoxivtWGfVhGXipMXd1gaaiDT2L/cNiQosiOEA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/drawing@0.10.0':
    resolution: {integrity: sha512-BJSpBegnxOsO1ysNcZFqE8IV54xbcCxkFKF8lJWrcj52teqZ0R4hTcELxHqrK2qem/Y/iH69GCe2YhpwTQE4bg==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/engine-formula@0.10.0':
    resolution: {integrity: sha512-QHN6JojjKxrxsg2wqEyl8bcsnlJL/vVB18SOUZQmzr023VuOGUP9seczk+qalh/dx2nLrp9ZGl/Uv03G6MhsHQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/engine-render@0.10.0':
    resolution: {integrity: sha512-2EdCXGLHPCGM2MAPQvp3AUvnsCQchygA9zPvyc3OnU2vUKjsVGkQFGBNppUBHsC17Jfq5yfZXB0WlsmrkDQm0A==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/find-replace@0.10.0':
    resolution: {integrity: sha512-hLD1HLErxZf124Kp7ugiz8z+aW7FSQzHeDGE0rhFLh9nVBfkrnhTSnmuyCgto2dJMLP7ekfbtJGaj0wmtfNrWA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/icons@0.4.6':
    resolution: {integrity: sha512-TAazMtFR+Jt82E3reHXNZXkSc8/5yV5A0QnSG3MIxhVtGMLIbBupceVjd46tCjogUd965iW+kZg4CjqvWvLU4A==}
    peerDependencies:
      react: '*'
      react-dom: '*'

  '@univerjs/network@0.10.0':
    resolution: {integrity: sha512-ZdEnSchkmQTAVvSirMgMylQlXiQeI6DRyNR32eC3PeLGSkrPuHnj+n2ziUVhb2o+juvGYUPllJG5N+Rd69e5YA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-advanced@0.10.0':
    resolution: {integrity: sha512-aiwSBcrexnl8ZOCeqaCMuv0A08wsCm0DNeTKY8FCrzR4u6g9odcFp33hVCDKxLnoLKejjVcbQSFfHiDIWqFYqg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-collaboration@0.10.0':
    resolution: {integrity: sha512-+NPKl1+gbxNdJho44PGz1/DYe+vAhs/uXDpRJY4jIRSYwF8l4VCkSsIxCPJOLF3cMu8uqhVrn/6AO+H69jr7/w==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-core@0.10.0':
    resolution: {integrity: sha512-AVZdX70nQge1X6QHJwq3iqJ6YEqj3tjW0xD8cc5mtXuf20pw4BAc3hmbL5ajvFDKlvUfxwFQ5GlKaNg80BFZIg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-drawing@0.10.0':
    resolution: {integrity: sha512-6Ol7FLnmKybGVm87Uw4wBAbmL3ap8JAiO85F+CrZL1M5nt9vzne5zQflR0/lB1vlQ/4p85IpuCM9KDFIHTSu5Q==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-hyper-link@0.10.0':
    resolution: {integrity: sha512-Nc+sOMvDJY9zc2roBxd/8fAzlA78s60A+hyY5DExYELa4u+enIUgpzTBmJTnCgrlc0Avh0QhMTk0dCtichK5iQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-node-core@0.10.0':
    resolution: {integrity: sha512-ppjwmk+J7Icbm0TDxpwj0fSyWLN5XVrygWLpoIUzHvLRJM/y3BO0KiMfvDXGNWTTUpiq48dRuh+QdkbOFPWdKg==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/preset-docs-thread-comment@0.10.0':
    resolution: {integrity: sha512-QAj9O6TR5mWxnvVIIhy1W+G3pJ6Crxy1uMa0+AXxzN6DEy2kPKJ491vMeO657AHq9u+QtF57cp1REKzn9a5X2w==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-advanced@0.10.0':
    resolution: {integrity: sha512-S6BB0UCjrZv2pJz5GTV7ogY5BtOXIicCSj5w2K1Pc1SdWvDmH66+K4l1mA73cdgCi9WSfCOiHM19Tf4RaozKJQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-collaboration@0.10.0':
    resolution: {integrity: sha512-bbLVsS5OlFlkoUg0dtxSu+B4qYgv8Z1zCPRnolq/CTi7TekGzAmeB+dShU5H4Sn1kekBG6AZdgwaeJ9YFnpNNA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-conditional-formatting@0.10.0':
    resolution: {integrity: sha512-69OxZQvf+aqf0jBpEk4nnSGwFIWyPhZYMB9/P1O/MgZGGNfIPXRLvTfs/gIaFsqteWfSbLG8+1u37TgWc4tRCg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-core@0.10.0':
    resolution: {integrity: sha512-/MdJpb7WiexaE/uKbD1zLSOlRkxiNN4LmZuYcseYud40LIds5c7MAk2EKYS/gf1dMzdgBEqpiPW4Sn0gPjDtEg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-data-validation@0.10.0':
    resolution: {integrity: sha512-7jA7ALVENTykbWCOhORgmlsGlee5WV7n8eWsQVQ4+LTOhcPb7nmp7SvS6i1nEf/yyKxxys2uAtz9ULirMTFQZg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-drawing@0.10.0':
    resolution: {integrity: sha512-aJ1Zeto6GXhQT43LTT8a89RK2ErOclHr34u70Qw6d1kElQlTcsI4QM9RS5I+AdfIemYM5HWbdbgJCZlIjBLy+Q==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-filter@0.10.0':
    resolution: {integrity: sha512-EQTOLYxfdZHKWRkPY+1wGrH7ekNAx46LhcXVMx5UPYry98sIiOucmE7jRsJBtV5bH1aJX5FyC88DkuRrP31hTg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-find-replace@0.10.0':
    resolution: {integrity: sha512-IuOEu3ZyLeT4dCacmCvYWc9c17W1AojYzjtIl+ZlRUvk0bH7bVAyI/DpgABCQPdrEl0TxdLY/t8VkZRGLHfFrQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-hyper-link@0.10.0':
    resolution: {integrity: sha512-sKwkyX4Wudr/VXBVW8IqHdflUrzgm6THec1+9um5kcdk4Pcti0foVese4bYN8RitNKCCwgJClA29Iyaf3aoRRw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-node-core@0.10.0':
    resolution: {integrity: sha512-HX7zHFZji7K3LLtyn1nHsFUdLh+I8/CkbaUEfauRflq4O0PKg+5m6SMZP4Wo/3fVWCYeh7kmhUlRl7I36nCLFQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-note@0.10.0':
    resolution: {integrity: sha512-V508TUrvz6Pqc1JgMJgwdYWx0d7LiDas0wqMwLcqgsjL/ROiLXZn18ENZ4H18Al6xpw/QeSapq4u/L0/J+sztA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-sort@0.10.0':
    resolution: {integrity: sha512-5XH6djq0NVsFMeg8dFr9wpWu6qeIu/ZXJ/2fg3o9DGBtucDdFpq2gLCmZywmOYrprQpf9VverNcWSViTOgI/zg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-table@0.10.0':
    resolution: {integrity: sha512-2Zua3n9GE6N04lZjxd73A1roERxOHaw2ukkkZZqI9o8urDw/oIqofFRLEli25dqq4EkP1PKUgEAhH6ceuGVlCA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/preset-sheets-thread-comment@0.10.0':
    resolution: {integrity: sha512-GpiCok9uQ6ymXGXl8JUtToRJSbEpryE//Porn794ObbFhnpTKIt8T4VS0cvzPdsE2gUytlwKvJ+oCVCpM1pnEQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/presets@0.10.0':
    resolution: {integrity: sha512-d4Zhnc1QFVmlbWPPslTLarCeLdsnk0eeVwIY46Q+seKj50eeh4WJEDcP6l0f4g/guO2tw4FouycVnZK3L5I+rg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/protocol@0.1.46':
    resolution: {integrity: sha512-nTkNocMt1XItBierjz7J4bz0Ye+SlUz7SDOyfqJB4FPpvDnBVGQH9NXbZbTrKrvskUzOayb7hGOkKNZyHaSQOw==}
    engines: {node: '>=18.0.0', pnpm: '>=10.0.0'}
    peerDependencies:
      '@grpc/grpc-js': '>=1'
      rxjs: '>=7.8'

  '@univerjs/protocol@0.1.47-alpha.0':
    resolution: {integrity: sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==}
    engines: {node: '>=18.0.0', pnpm: '>=10.0.0'}
    peerDependencies:
      '@grpc/grpc-js': '>=1'
      rxjs: '>=7.8'

  '@univerjs/rpc-node@0.10.0':
    resolution: {integrity: sha512-VwqJ+4TNrmp+SaEKv58ypj3e1lNphq5fZtBZVKcLrMfIUOx3kD/4oAWT3Yjv8VB+JA4TiqiTuft9E4aSgspZ/g==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/rpc@0.10.0':
    resolution: {integrity: sha512-43XwFUHaF0E/ugsp9cpOFPWODogEbq9HMxPOG/pX8r0gcu4T0oUUnPsXNMVJWqqiWhImPCzXVvFY4/e0Wct4gQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-conditional-formatting-ui@0.10.0':
    resolution: {integrity: sha512-Tk0FdScoeAegrFfpPUsc+LiIvfM9KWXdhSu3cPcJjgQpXkHYvF/nWmsw/wGEY6x/Y/381RJJRoU6NZsNZmt8vA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-conditional-formatting@0.10.0':
    resolution: {integrity: sha512-5Ki9NnH8Z5VkV2LC5cCi9W0LV3V84Ovn7+OUJFt60/7J1AC+OKW2j1GA04LoJjhQxrxC3zqodUcUIiTy64SHng==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-data-validation-ui@0.10.0':
    resolution: {integrity: sha512-TaAaYem39uknNJKY9i7Bdp9ECBlr3ngL8xmMcO6krXfNTds0M+hd1YL2Crh78LrRmMg33MGx3I/PL9uVVzT/sw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-data-validation@0.10.0':
    resolution: {integrity: sha512-BBhM6/gcRfKp6ihHikRU9zMORj/YtaIYb5pTBHNB+Oiogn3dk33Ap70uESZ2SfBKmZZlZ9G0thWaD2jhGyKjfA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-drawing-ui@0.10.0':
    resolution: {integrity: sha512-flcQfpr5K8f8KhUWhu7UZcjsFGI7VvM4QlfbYGrepFZkqttkaxlM+8jDu6SP1aDFYidmR8ugq58ZTm00TkSj3g==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-drawing@0.10.0':
    resolution: {integrity: sha512-PWx1Y8hKrL1fM9bloTPC+ponKsOrm3+JYqlbVGkfMWx8xhtBxkuehLpWAnfzZhKt96NHTTcPu8FD8mlp23r+UA==}

  '@univerjs/sheets-filter-ui@0.10.0':
    resolution: {integrity: sha512-UR97JCvJ12jtOv1SbNqYcgDLYyz4tcPxEXO7RX8Aola1avWBC06+86KiOSaldKZ7iomXRo1/7De1Za0M1sSIyg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-filter@0.10.0':
    resolution: {integrity: sha512-3y17tP27Sw4h6N8fAaiopwKzn1O5a5Y8S8I0nGyg8ODFmnp0OHPEivxMDnQ/Xx+SXLmU9t2C2cUD2OgIhA6u8g==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-find-replace@0.10.0':
    resolution: {integrity: sha512-8P3njEWXem2nj2lEZqUI7W+9LEjB51+8P0mTq0d3SFoY4qOLZGONB5IQgSpEUqMq8PlbU0m730zzi6qawmznwQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-formula-ui@0.10.0':
    resolution: {integrity: sha512-cCVI8AilPJ9SmqGi0FUJImLaa6k5pSgkhdWq80XCOmj43FfpQXXEfitq1gZxxu4HGV5BB/GxLW9MAYZI8U2Atw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-formula@0.10.0':
    resolution: {integrity: sha512-GFJO1BO507pDencr6T/YsAuE0VK0A8BB0NXBMmYfKRXqviCoVDkEVp9Ln7PVsTfDmM5nUhgXaBVg4DTBagNf+Q==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-graphics@0.10.0':
    resolution: {integrity: sha512-BT3ZOrqoMozeeAScMOWOUwLqAgoM5A1VwAS7a28bFnI37FF2jYBOe/OxMZggaHsiJ0+BiZZnQ6PqyiItU3RmkQ==}

  '@univerjs/sheets-hyper-link-ui@0.10.0':
    resolution: {integrity: sha512-Xbschne5OYO6rCtXlArRS01HJlbBpxA17VEyUIHP0V0sbXtEym2qlRpgrzthDzKEgYgl8YujttxljiQdslykbg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-hyper-link@0.10.0':
    resolution: {integrity: sha512-9saYJsIOR0naqCgAnIpe1KsuQYbYHL0CuGir3vskIVf67wtp5RdqdE6W/qWkXBFI9Nyn/fn1laP+L1uriKolYQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-note-ui@0.10.0':
    resolution: {integrity: sha512-yil/uVagYwjxyQWsy7LKQp2EsouXPfZirReV19OtCoamu/EuOIRAuySlXqQLayMcXjqVWPqhXBlvYT2iYsJo9Q==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-note@0.10.0':
    resolution: {integrity: sha512-qXGttMK/FoGIX+n1aL0WnYmXv3m+aTRVOH0sy+6i/6IQQecSanLsJj8V2M0NqsO2gQYlXkW5UR6Bljx3SXRCuQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-numfmt-ui@0.10.0':
    resolution: {integrity: sha512-NKazTe3Dd1Hx3JHFxQoiAjMj/wZXGczZ4512JOpn4e18fMHEREhXjZgTm6CLE6KeciqBqpDK4YS6TSL6BPsvMA==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-numfmt@0.10.0':
    resolution: {integrity: sha512-oJdy1C/rAFsQlXUg6oHpE6frr0Je2Vw/aNqO/4epvwkQzdCnC2SoE4qmS3uT2nyWZrn39ySjnR9rpUjPzHt93w==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-sort-ui@0.10.0':
    resolution: {integrity: sha512-ZkgymOThdWROemXyLqjpBRVULZh3wk4ihKY3TRRFqNvvyYUpjnqkrMplnJ2hSr6e0gg5bjCytgVgYz23RKEJBw==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-sort@0.10.0':
    resolution: {integrity: sha512-y57eghfhEzkdcwh5bTOHFusEDgNmylwIl33CZdueg9SqyQlPUcN/nCtfF6BXM4uzR1NwhUvCkIGRR3VRSOU4hw==}

  '@univerjs/sheets-table-ui@0.10.0':
    resolution: {integrity: sha512-qWCC0H0k25naGSri2TtbTBQIcDPKpwbdRvZKDhjkAsop4HGdP7meC8JJkxIeIPEnos8TzDJomHk7NeCN4STm+g==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-table@0.10.0':
    resolution: {integrity: sha512-BIjnR5rRZX23Mc8vz5v7tgzTRT84swbVV/u19A/6ON4GhWN4v6ZSOUzPMFbYZOT7Cn1PyeoneV0vSz4DX5v62Q==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-thread-comment-ui@0.10.0':
    resolution: {integrity: sha512-rFQ4bpQb1A62WKPKoLyxH2mV4RcpR1gNC5jev0ZvBGf7gEPJ2QDCnLhSgOh7joEUu7VnMEWXEYcWan3OFE+C/Q==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets-thread-comment@0.10.0':
    resolution: {integrity: sha512-X0Jn/nL4ereNgyIPL/6GQ5+VRusO9Q0i3Ust0y+peja8K/GiKB4bpAdg16ckLcYNdDLIWPO5yewj64DwRIP/kA==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/sheets-ui@0.10.0':
    resolution: {integrity: sha512-GagiK+OpqOu8wa+iNmrQdUTDl5XsAphzf+lAqK4wCInkg0Knp5ODtv+q0LdlrBuX/CjmRvutz5a2DrFY9fYu1w==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/sheets@0.10.0':
    resolution: {integrity: sha512-I3taVVx2l3T5u1zhzGLWxfPVnjpkPtGMDE0ZdXGmzWrnljcFx4hUHTTh19wcsIKTAFb08sEWbcJ/CUzA5I//xg==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/telemetry@0.10.0':
    resolution: {integrity: sha512-EPDcYMzajduxpBPQws7B58jZp5bA9AXxKY0ITmm6Rx954kjbcFfmN3Y+E9IA6kc1DGU5Qc7Eg634/3gaACJEGw==}

  '@univerjs/themes@0.10.0':
    resolution: {integrity: sha512-JbpNUEGouhug0WmrbOmt5eE2QHOAPXQf7O7tLlZ12XaTtVjp50QfyTSk8qcHYCYHTnd5NbjzqnwBmomPT/lsZw==}

  '@univerjs/thread-comment-ui@0.10.0':
    resolution: {integrity: sha512-9+/Ih/fYFKyYqWGKjHfnd3E1GmX/iZ6mY1yhvKpv1uBuqSXRckh/Avvmgwjr8tgzjAZlR626DsN/PQim7Tx8Fg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@univerjs/thread-comment@0.10.0':
    resolution: {integrity: sha512-IgBZln/T6M9FKeI5RlzNVKjVQ8rigb6FkGeVY8HelpzidMdcjsbBzpjqYZ83xZ0S2agprAfJ4DTcpEc4LWjNnQ==}
    peerDependencies:
      rxjs: '>=7.0.0'

  '@univerjs/ui@0.10.0':
    resolution: {integrity: sha512-HXQ8nggYMYi1T1e4KY0pW3cV5roLj4NiTOTgi2ZuBY0VErEZ88cQjYKhl7CeRMbuLIWfMjQW9OweDy87+MOIxQ==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      rxjs: '>=7.0.0'

  '@wendellhu/redi@1.0.0':
    resolution: {integrity: sha512-FZhvlxYVdRh2omad2iJot9A+9lXqjF+V30crFtj60W2141bBq+WiU9AiLqGWDW1ePS/i4iqaccWhj3t78W8S3A==}
    engines: {node: '>=22.12.0'}
    peerDependencies:
      react: '>=16.8.0'
    peerDependenciesMeta:
      react:
        optional: true

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  aria-hidden@1.2.6:
    resolution: {integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==}
    engines: {node: '>=10'}

  async-lock@1.4.1:
    resolution: {integrity: sha512-Az2ZTpuytrtqENulXwO3GGv1Bztugx6TT37NIo7imr/Qo0gsYiGtSdBa2B6fsXhTpVZDNfu1Qn3pk531e3q+nQ==}

  cjk-regex@3.3.0:
    resolution: {integrity: sha512-o9QeA4DIiljRGO3mXzkQXBttzE6XRGZG99V9F8uqrdqKo5RHTFe8w+pk1aOMB/wxQ7qQ8J7WoTagabTabPgl8A==}
    engines: {node: '>=16'}

  class-variance-authority@0.7.1:
    resolution: {integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==}

  classnames@2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  collapse-white-space@2.1.0:
    resolution: {integrity: sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  css-box-model@1.2.1:
    resolution: {integrity: sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  decimal.js@10.6.0:
    resolution: {integrity: sha512-YpgQiITW3JXGntzdUmyUR1V812Hn8T1YVXhCu+wO3OpS4eU9l4YdD3qjyiKdV6mvV29zapkMeD390UVEf2lkUg==}

  detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  echarts-wordcloud@2.1.0:
    resolution: {integrity: sha512-Kt1JmbcROgb+3IMI48KZECK2AP5lG6bSsOEs+AsuwaWJxQom31RTNd6NFYI01E/YaI1PFZeueaupjlmzSQasjQ==}
    peerDependencies:
      echarts: ^5.0.1

  echarts@5.6.0:
    resolution: {integrity: sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  esbuild@0.25.8:
    resolution: {integrity: sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}

  fast-equals@4.0.3:
    resolution: {integrity: sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg==}

  fdir@6.4.6:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  franc-min@6.2.0:
    resolution: {integrity: sha512-1uDIEUSlUZgvJa2AKYR/dmJC66v/PvGQ9mWfI9nOr/kPpMFyvswK0gPXOwpYJYiYD008PpHLkGfG58SPjQJFxw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  immediate@3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  kdbush@4.0.2:
    resolution: {integrity: sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==}

  lie@3.1.1:
    resolution: {integrity: sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==}

  localforage@1.10.0:
    resolution: {integrity: sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}

  long@5.3.2:
    resolution: {integrity: sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  memoize-one@5.2.1:
    resolution: {integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==}

  n-gram@2.0.2:
    resolution: {integrity: sha512-S24aGsn+HLBxUGVAUFOwGpKs7LBcG4RudKU//eWzt/mQ97/NMKQxDWHyHx63UNWk/OOdihgmzoETn1tf5nQDzQ==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.1.5:
    resolution: {integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==}
    engines: {node: ^18 || >=20}
    hasBin: true

  numfmt@3.2.3:
    resolution: {integrity: sha512-q5vjJSiuomxYNNVhB/TWqjtctZz+fnscUchvwonutXZ/neY2XLw6z4q3DS4ijLDrP5Y/tgrVeP1/7PjgHRoZuw==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  opentype.js@1.3.4:
    resolution: {integrity: sha512-d2JE9RP/6uagpQAVtJoF0pJJA/fgai89Cc50Yp0EJHk+eLp6QQ7gBoblsnubRULNY132I0J1QKMJ+JTbMqz4sw==}
    engines: {node: '>= 8.0.0'}
    hasBin: true

  ot-json1@1.0.2:
    resolution: {integrity: sha512-IhxkqVWQqlkWULoi/Q2AdzKk0N5vQRbUMUwubFXFCPcY4TsOZjmp2YKrk0/z1TeiECPadWEK060sdFdQ3Grokg==}

  ot-text-unicode@4.0.0:
    resolution: {integrity: sha512-W7ZLU8QXesY2wagYFv47zErXud3E93FGImmSGJsQnBzE+idcPPyo2u2KMilIrTwBh4pbCizy71qRjmmV6aDhcQ==}

  pako@2.1.0:
    resolution: {integrity: sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@4.0.3:
    resolution: {integrity: sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==}
    engines: {node: '>=12'}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  protobufjs@7.5.3:
    resolution: {integrity: sha512-sildjKwVqOI2kmFDiXQ6aEB0fjYTafpEvIBs8tOR8qI4spuL9OPROLVu2qZqi/xgCfsHIwVqlaF8JBjWFHnKbw==}
    engines: {node: '>=12.0.0'}

  quickselect@3.0.0:
    resolution: {integrity: sha512-XdjUArbK4Bm5fLLvlm5KpTFOiOThgfWWI4axAZDWg4E/0mKdZyI9tNEfds27qCi1ze/vwTR16kvmmGhRra3c2g==}

  raf-schd@4.0.3:
    resolution: {integrity: sha512-tQkJl2GRWh83ui2DiPTJz9wEiMN20syf+5oKfB03yYP7ioZcJwsIK8FjrtLwH1m7C7e+Tt2yYBlrOpdT+dyeIQ==}

  rbush@4.0.1:
    resolution: {integrity: sha512-IP0UpfeWQujYC8Jg162rMNc01Rf0gWMMAb2Uxus/Q0qOFw4lCcq6ZnQEZwUoJqWyUGJ9th7JjwI4yIWo+uvoAQ==}

  rc-dropdown@4.2.1:
    resolution: {integrity: sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'

  rc-menu@9.16.1:
    resolution: {integrity: sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-motion@2.9.5:
    resolution: {integrity: sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-overflow@1.4.1:
    resolution: {integrity: sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-picker@4.11.3:
    resolution: {integrity: sha512-MJ5teb7FlNE0NFHTncxXQ62Y5lytq6sh5nUw0iH8OkHL/TjARSEvSHpr940pWgjGANpjCwyMdvsEV55l5tYNSg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true

  rc-resize-observer@1.4.3:
    resolution: {integrity: sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@5.44.4:
    resolution: {integrity: sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-virtual-list@3.19.1:
    resolution: {integrity: sha512-DCapO2oyPqmooGhxBuXHM4lFuX+sshQwWqqkuyFA+4rShLe//+GEPVwiDgO+jKtKHtbeYwZoNvetwfHdOf+iUQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-beautiful-dnd@13.1.1:
    resolution: {integrity: sha512-0Lvs4tq2VcrEjEgDXHjT98r+63drkKEgqyxdA7qD3mvKwga6a5SscbdLPO2IExotU1jW8L0Ksdl0Cj2AF67nPQ==}
    deprecated: 'react-beautiful-dnd is now deprecated. Context and options: https://github.com/atlassian/react-beautiful-dnd/issues/2672'
    peerDependencies:
      react: ^16.8.5 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.5 || ^17.0.0 || ^18.0.0

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-draggable@4.5.0:
    resolution: {integrity: sha512-VC+HBLEZ0XJxnOxVAZsdRi8rD04Iz3SiiKOoYzamjylUcju/hP9np/aZdLHf/7WOD268WMoNJMvYfB5yAK45cw==}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'

  react-grid-layout@1.5.2:
    resolution: {integrity: sha512-vT7xmQqszTT+sQw/LfisrEO4le1EPNnSEMVHy6sBZyzS3yGkMywdOd+5iEFFwQwt0NSaGkxuRmYwa1JsP6OJdw==}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  react-redux@7.2.9:
    resolution: {integrity: sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==}
    peerDependencies:
      react: ^16.8.3 || ^17 || ^18
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  react-remove-scroll-bar@2.3.8:
    resolution: {integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.7.1:
    resolution: {integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-resizable@3.0.5:
    resolution: {integrity: sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w==}
    peerDependencies:
      react: '>= 16.3'

  react-style-singleton@2.2.3:
    resolution: {integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}

  regexp-util@2.0.3:
    resolution: {integrity: sha512-GP6h9OgJmhAZpb3dbNbXTfRWVnGcoMhWRZv/HxgM4/qCVqs1P9ukQdYxaUhjWBSAs9oJ/uPXUUvGT1VMe0Bs0Q==}
    engines: {node: '>=16'}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  rollup@4.46.1:
    resolution: {integrity: sha512-33xGNBsDJAkzt0PvninskHlWnTIPgDtTwhg0U38CUoNP/7H6wI2Cz6dUeoNPbjdTdsYTGuiFFASuUOWovH0SyQ==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rxjs@7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  sonner@2.0.6:
    resolution: {integrity: sha512-yHFhk8T/DK3YxjFQXIrcHT1rGEeTLliVzWbO0xN8GberVun2RiBnxAjXAYpZrqwEVHBG9asI/Li8TAAhN9m59Q==}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string.prototype.codepointat@0.2.1:
    resolution: {integrity: sha512-2cBVCj6I4IOvEnjgO/hWqXjqBGsY+zwPmHl12Srk9IXSZ56Jwwmy+66XO5Iut/oQVR7t5ihYdLB0GMa4alEUcg==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  tailwind-merge@3.3.1:
    resolution: {integrity: sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g==}

  tiny-inflate@1.0.3:
    resolution: {integrity: sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==}

  tiny-invariant@1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  trigram-utils@2.0.1:
    resolution: {integrity: sha512-nfWIXHEaB+HdyslAfMxSqWKDdmqY9I32jS7GnqpdWQnLH89r6A5sdk3fDVYqGAZ0CrT8ovAFSAo6HRiWcWNIGQ==}

  tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@7.8.0:
    resolution: {integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==}

  unicode-regex@4.1.2:
    resolution: {integrity: sha512-30Y3tQ8OUxceQjsEJHzNh20lLYZX6ZwQyUOHBUdN1UPKQWH3AvH20aUADWa1gEz2lQPTSQ/l2ZqdM4FjFNMJsQ==}
    engines: {node: '>=16'}

  unicount@1.1.0:
    resolution: {integrity: sha512-RlwWt1ywVW4WErPGAVHw/rIuJ2+MxvTME0siJ6lk9zBhpDfExDbspe6SRlWT3qU6AucNjotPl9qAJRVjP7guCQ==}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-memo-one@1.1.3:
    resolution: {integrity: sha512-g66/K7ZQGYrI6dy8GLpVcMsBp4s17xNkYJVSMvTEevGy3nDxHOfE6z8BVE22+5G5x7t3+bhzrlTDB7ObrEE0cQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  use-sidecar@1.1.3:
    resolution: {integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  uuid@11.1.0:
    resolution: {integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==}
    hasBin: true

  vite@7.0.6:
    resolution: {integrity: sha512-MHFiOENNBd+Bd9uvc8GEsIzdkn1JxMmEeYX35tI3fv0sJBUTfW5tQsoaOwuY4KhBI09A3dUJ/DXf2yxPVPUceg==}
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  zrender@5.6.1:
    resolution: {integrity: sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==}

snapshots:

  '@babel/runtime@7.28.2': {}

  '@esbuild/aix-ppc64@0.25.8':
    optional: true

  '@esbuild/android-arm64@0.25.8':
    optional: true

  '@esbuild/android-arm@0.25.8':
    optional: true

  '@esbuild/android-x64@0.25.8':
    optional: true

  '@esbuild/darwin-arm64@0.25.8':
    optional: true

  '@esbuild/darwin-x64@0.25.8':
    optional: true

  '@esbuild/freebsd-arm64@0.25.8':
    optional: true

  '@esbuild/freebsd-x64@0.25.8':
    optional: true

  '@esbuild/linux-arm64@0.25.8':
    optional: true

  '@esbuild/linux-arm@0.25.8':
    optional: true

  '@esbuild/linux-ia32@0.25.8':
    optional: true

  '@esbuild/linux-loong64@0.25.8':
    optional: true

  '@esbuild/linux-mips64el@0.25.8':
    optional: true

  '@esbuild/linux-ppc64@0.25.8':
    optional: true

  '@esbuild/linux-riscv64@0.25.8':
    optional: true

  '@esbuild/linux-s390x@0.25.8':
    optional: true

  '@esbuild/linux-x64@0.25.8':
    optional: true

  '@esbuild/netbsd-arm64@0.25.8':
    optional: true

  '@esbuild/netbsd-x64@0.25.8':
    optional: true

  '@esbuild/openbsd-arm64@0.25.8':
    optional: true

  '@esbuild/openbsd-x64@0.25.8':
    optional: true

  '@esbuild/openharmony-arm64@0.25.8':
    optional: true

  '@esbuild/sunos-x64@0.25.8':
    optional: true

  '@esbuild/win32-arm64@0.25.8':
    optional: true

  '@esbuild/win32-ia32@0.25.8':
    optional: true

  '@esbuild/win32-x64@0.25.8':
    optional: true

  '@flatten-js/interval-tree@1.1.3': {}

  '@floating-ui/core@1.7.2':
    dependencies:
      '@floating-ui/utils': 0.2.10

  '@floating-ui/dom@1.7.2':
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/utils': 0.2.10

  '@floating-ui/react-dom@2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/dom': 1.7.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@floating-ui/utils@0.2.10': {}

  '@grpc/grpc-js@1.13.4':
    dependencies:
      '@grpc/proto-loader': 0.7.15
      '@js-sdsl/ordered-map': 4.4.2

  '@grpc/proto-loader@0.7.15':
    dependencies:
      lodash.camelcase: 4.3.0
      long: 5.3.2
      protobufjs: 7.5.3
      yargs: 17.7.2

  '@js-sdsl/ordered-map@4.4.2': {}

  '@noble/ed25519@2.3.0': {}

  '@noble/hashes@1.8.0': {}

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@radix-ui/primitive@1.1.2': {}

  '@radix-ui/react-arrow@1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-collection@1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-context@1.1.2(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-dialog@1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-portal': 1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      aria-hidden: 1.2.6
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.7.1(@types/react@19.1.9)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-direction@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-dismissable-layer@1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-escape-keydown': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-dropdown-menu@2.1.15(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-menu': 2.1.15(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-focus-scope@1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-hover-card@1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-popper': 1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-id@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-menu@2.1.15(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-popper': 1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-roving-focus': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      aria-hidden: 1.2.6
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.7.1(@types/react@19.1.9)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-popover@1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-popper': 1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      aria-hidden: 1.2.6
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.7.1(@types/react@19.1.9)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-popper@1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-arrow': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-rect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/rect': 1.1.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-portal@1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-presence@1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-primitive@2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-roving-focus@1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-separator@1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-slot@1.2.3(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-tooltip@1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-popper': 1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.9(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.4(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-visually-hidden': 1.2.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/rect': 1.1.1
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.9)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.9)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/react-visually-hidden@1.2.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  '@radix-ui/rect@1.1.1': {}

  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.28.2
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rc-component/trigger@2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.28.2
      '@rc-component/portal': 1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@rollup/rollup-android-arm-eabi@4.46.1':
    optional: true

  '@rollup/rollup-android-arm64@4.46.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.46.1':
    optional: true

  '@rollup/rollup-darwin-x64@4.46.1':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.46.1':
    optional: true

  '@rollup/rollup-freebsd-x64@4.46.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.46.1':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.46.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.46.1':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-ppc64-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.46.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.46.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.46.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.46.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.46.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.46.1':
    optional: true

  '@types/estree@1.0.8': {}

  '@types/hoist-non-react-statics@3.3.7(@types/react@19.1.9)':
    dependencies:
      '@types/react': 19.1.9
      hoist-non-react-statics: 3.3.2

  '@types/node@24.1.0':
    dependencies:
      undici-types: 7.8.0

  '@types/react-redux@7.1.34':
    dependencies:
      '@types/hoist-non-react-statics': 3.3.7(@types/react@19.1.9)
      '@types/react': 19.1.9
      hoist-non-react-statics: 3.3.2
      redux: 4.2.1

  '@types/react@19.1.9':
    dependencies:
      csstype: 3.1.3

  '@univerjs-pro/collaboration-client-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      crypto-js: 4.2.0
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/collaboration-client@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/telemetry': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      crypto-js: 4.2.0
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/collaboration@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      uuid: 11.1.0
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs-pro/docs-exchange-client@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react
      - react-dom
      - rxjs

  '@univerjs-pro/docs-print@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/print': 0.10.0
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
    optionalDependencies:
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom
      - rxjs

  '@univerjs-pro/edit-history-loader@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/edit-history-viewer': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-pivot': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-sparkline': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-sparkline-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-numfmt': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/edit-history-viewer@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/sheets-sparkline': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/engine-chart@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/engine-formula@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/engine-pivot@0.10.0': {}

  '@univerjs-pro/exchange-client@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      pako: 2.1.0
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/license@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@noble/ed25519': 2.3.0
      '@noble/hashes': 1.8.0
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/print@0.10.0': {}

  '@univerjs-pro/sheets-chart-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/engine-chart': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-chart': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      echarts: 5.6.0
      echarts-wordcloud: 2.1.0(echarts@5.6.0)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/sheets-chart@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/engine-chart': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/sheets-exchange-client@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react
      - react-dom
      - rxjs

  '@univerjs-pro/sheets-pivot-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/engine-pivot': 0.10.0
      '@univerjs-pro/sheets-pivot': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-beautiful-dnd: 13.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-native

  '@univerjs-pro/sheets-pivot@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/engine-pivot': 0.10.0
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/sheets-print@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/print': 0.10.0
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    optionalDependencies:
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/sheets-sparkline-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/sheets-sparkline': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-graphics': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs-pro/sheets-sparkline@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs-pro/thread-comment-datasource@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react
      - react-dom

  '@univerjs/core@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/protocol': 0.1.46(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/themes': 0.10.0
      '@wendellhu/redi': 1.0.0(react@18.3.1)
      async-lock: 1.4.1
      dayjs: 1.11.13
      fast-diff: 1.3.0
      kdbush: 4.0.2
      lodash-es: 4.17.21
      nanoid: 5.1.5
      numfmt: 3.2.3
      ot-json1: 1.0.2
      rbush: 4.0.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'

  '@univerjs/data-validation@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/design@0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-dialog': 1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-dropdown-menu': 2.1.15(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-hover-card': 1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-popover': 1.1.14(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-separator': 1.1.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.9)(react@18.3.1)
      '@radix-ui/react-tooltip': 1.2.7(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@rc-component/trigger': 2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/themes': 0.10.0
      class-variance-authority: 0.7.1
      clsx: 2.1.1
      dayjs: 1.11.13
      rc-dropdown: 4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-menu: 9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-picker: 4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-virtual-list: 3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-grid-layout: 1.5.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      sonner: 2.0.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      tailwind-merge: 3.3.1
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
      - date-fns
      - luxon
      - moment

  '@univerjs/docs-drawing-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/docs-drawing@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs/docs-hyper-link-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/docs-hyper-link@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs/docs-thread-comment-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/docs-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/docs@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/drawing-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/drawing@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      ot-json1: 1.0.2
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/engine-formula@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@flatten-js/interval-tree': 1.1.3
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      decimal.js: 10.6.0
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/engine-render@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@floating-ui/dom': 1.7.2
      '@floating-ui/utils': 0.2.10
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      cjk-regex: 3.3.0
      franc-min: 6.2.0
      opentype.js: 1.3.4
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/find-replace@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/icons@0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@univerjs/network@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/preset-docs-advanced@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/docs-exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/docs-print': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-docs-collaboration@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-docs-core@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-docs-drawing@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-docs-hyper-link@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-hyper-link-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-docs-node-core@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc-node': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/preset-docs-thread-comment@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs-thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-advanced@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/engine-chart': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/license': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-chart': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-chart-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/sheets-exchange-client': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/sheets-pivot': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-pivot-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/sheets-print': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/sheets-sparkline': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/sheets-sparkline-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-graphics': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-native

  '@univerjs/preset-sheets-collaboration@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs-pro/collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs-pro/collaboration-client-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/edit-history-loader': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/edit-history-viewer': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs-pro/thread-comment-datasource': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-advanced': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-native

  '@univerjs/preset-sheets-conditional-formatting@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-core@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/network': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-numfmt': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-numfmt-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-data-validation@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-drawing@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-filter@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-find-replace@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/find-replace': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-find-replace': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-hyper-link@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-node-core@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc-node': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-numfmt': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-sort': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/preset-sheets-note@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-note': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-note-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-sort@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-sort': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-sort-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-table@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-table': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-table-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/preset-sheets-thread-comment@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/sheets-thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment

  '@univerjs/presets@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/preset-docs-advanced': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-docs-collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-docs-core': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-docs-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-docs-node-core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/preset-docs-thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-advanced': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-collaboration': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-core': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-find-replace': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-node-core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/preset-sheets-note': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-sort': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-table': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/preset-sheets-thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-native

  '@univerjs/protocol@0.1.46(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)':
    dependencies:
      '@grpc/grpc-js': 1.13.4
      rxjs: 7.8.2

  '@univerjs/protocol@0.1.47-alpha.0(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)':
    dependencies:
      '@grpc/grpc-js': 1.13.4
      rxjs: 7.8.2

  '@univerjs/rpc-node@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/rpc@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-conditional-formatting-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-conditional-formatting': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-conditional-formatting@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-data-validation-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@flatten-js/interval-tree': 1.1.3
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-numfmt': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-data-validation@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.46(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-drawing-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-drawing@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/drawing': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs/sheets-filter-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-filter': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-filter@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-find-replace@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/find-replace': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react
      - react-dom

  '@univerjs/sheets-formula-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-formula@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-graphics@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react
      - react-dom
      - rxjs

  '@univerjs/sheets-hyper-link-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-data-validation': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-hyper-link': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-hyper-link@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-note-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-note': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-note@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-numfmt-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-numfmt': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-numfmt@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-sort-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-sort': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-sort@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs/sheets-table-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-formula-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/sheets-sort': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-table': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-table@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-thread-comment-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets-thread-comment@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/sheets-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/protocol': 0.1.46(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/sheets': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/telemetry': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/sheets@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/engine-formula': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/protocol': 0.1.46(@grpc/grpc-js@1.13.4)(rxjs@7.8.2)
      '@univerjs/rpc': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/telemetry@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'
      - rxjs

  '@univerjs/themes@0.10.0': {}

  '@univerjs/thread-comment-ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/docs-ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(@wendellhu/redi@1.0.0(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/thread-comment': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/ui': 0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)
      react: 18.3.1
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - '@wendellhu/redi'
      - date-fns
      - luxon
      - moment
      - react-dom

  '@univerjs/thread-comment@0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@wendellhu/redi'

  '@univerjs/ui@0.10.0(@grpc/grpc-js@1.13.4)(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rxjs@7.8.2)':
    dependencies:
      '@univerjs/core': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/design': 0.10.0(@types/react@19.1.9)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@univerjs/engine-render': 0.10.0(@grpc/grpc-js@1.13.4)(@wendellhu/redi@1.0.0(react@18.3.1))(rxjs@7.8.2)
      '@univerjs/icons': 0.4.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@wendellhu/redi': 1.0.0(react@18.3.1)
      localforage: 1.10.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rxjs: 7.8.2
    transitivePeerDependencies:
      - '@grpc/grpc-js'
      - '@types/react'
      - '@types/react-dom'
      - date-fns
      - luxon
      - moment

  '@wendellhu/redi@1.0.0(react@18.3.1)':
    optionalDependencies:
      react: 18.3.1

  ansi-regex@5.0.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  async-lock@1.4.1: {}

  cjk-regex@3.3.0:
    dependencies:
      regexp-util: 2.0.3
      unicode-regex: 4.1.2

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  classnames@2.5.1: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@2.1.1: {}

  collapse-white-space@2.1.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  crypto-js@4.2.0: {}

  css-box-model@1.2.1:
    dependencies:
      tiny-invariant: 1.3.3

  csstype@3.1.3: {}

  dayjs@1.11.13: {}

  decimal.js@10.6.0: {}

  detect-node-es@1.1.0: {}

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.28.2
      csstype: 3.1.3

  echarts-wordcloud@2.1.0(echarts@5.6.0):
    dependencies:
      echarts: 5.6.0

  echarts@5.6.0:
    dependencies:
      tslib: 2.3.0
      zrender: 5.6.1

  emoji-regex@8.0.0: {}

  esbuild@0.25.8:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.8
      '@esbuild/android-arm': 0.25.8
      '@esbuild/android-arm64': 0.25.8
      '@esbuild/android-x64': 0.25.8
      '@esbuild/darwin-arm64': 0.25.8
      '@esbuild/darwin-x64': 0.25.8
      '@esbuild/freebsd-arm64': 0.25.8
      '@esbuild/freebsd-x64': 0.25.8
      '@esbuild/linux-arm': 0.25.8
      '@esbuild/linux-arm64': 0.25.8
      '@esbuild/linux-ia32': 0.25.8
      '@esbuild/linux-loong64': 0.25.8
      '@esbuild/linux-mips64el': 0.25.8
      '@esbuild/linux-ppc64': 0.25.8
      '@esbuild/linux-riscv64': 0.25.8
      '@esbuild/linux-s390x': 0.25.8
      '@esbuild/linux-x64': 0.25.8
      '@esbuild/netbsd-arm64': 0.25.8
      '@esbuild/netbsd-x64': 0.25.8
      '@esbuild/openbsd-arm64': 0.25.8
      '@esbuild/openbsd-x64': 0.25.8
      '@esbuild/openharmony-arm64': 0.25.8
      '@esbuild/sunos-x64': 0.25.8
      '@esbuild/win32-arm64': 0.25.8
      '@esbuild/win32-ia32': 0.25.8
      '@esbuild/win32-x64': 0.25.8

  escalade@3.2.0: {}

  fast-diff@1.3.0: {}

  fast-equals@4.0.3: {}

  fdir@6.4.6(picomatch@4.0.3):
    optionalDependencies:
      picomatch: 4.0.3

  franc-min@6.2.0:
    dependencies:
      trigram-utils: 2.0.1

  fsevents@2.3.3:
    optional: true

  get-caller-file@2.0.5: {}

  get-nonce@1.0.1: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  immediate@3.0.6: {}

  is-fullwidth-code-point@3.0.0: {}

  js-tokens@4.0.0: {}

  kdbush@4.0.2: {}

  lie@3.1.1:
    dependencies:
      immediate: 3.0.6

  localforage@1.10.0:
    dependencies:
      lie: 3.1.1

  lodash-es@4.17.21: {}

  lodash.camelcase@4.3.0: {}

  long@5.3.2: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  memoize-one@5.2.1: {}

  n-gram@2.0.2: {}

  nanoid@3.3.11: {}

  nanoid@5.1.5: {}

  numfmt@3.2.3: {}

  object-assign@4.1.1: {}

  opentype.js@1.3.4:
    dependencies:
      string.prototype.codepointat: 0.2.1
      tiny-inflate: 1.0.3

  ot-json1@1.0.2:
    dependencies:
      ot-text-unicode: 4.0.0

  ot-text-unicode@4.0.0:
    dependencies:
      unicount: 1.1.0

  pako@2.1.0: {}

  picocolors@1.1.1: {}

  picomatch@4.0.3: {}

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  protobufjs@7.5.3:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 24.1.0
      long: 5.3.2

  quickselect@3.0.0: {}

  raf-schd@4.0.3: {}

  rbush@4.0.1:
    dependencies:
      quickselect: 3.0.0

  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      '@rc-component/trigger': 2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      '@rc-component/trigger': 2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-overflow: 1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      '@rc-component/trigger': 2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classnames: 2.5.1
      rc-overflow: 1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      dayjs: 1.11.13

  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      resize-observer-polyfill: 1.5.1

  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 18.3.1

  rc-virtual-list@3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      rc-util: 5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-beautiful-dnd@13.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      css-box-model: 1.2.1
      memoize-one: 5.2.1
      raf-schd: 4.0.3
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-redux: 7.2.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      redux: 4.2.1
      use-memo-one: 1.1.3(react@18.3.1)
    transitivePeerDependencies:
      - react-native

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-draggable@4.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-grid-layout@1.5.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      clsx: 2.1.1
      fast-equals: 4.0.3
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-draggable: 4.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-resizable: 3.0.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      resize-observer-polyfill: 1.5.1

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-redux@7.2.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      '@types/react-redux': 7.1.34
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-is: 17.0.2
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react-remove-scroll-bar@2.3.8(@types/react@19.1.9)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-style-singleton: 2.2.3(@types/react@19.1.9)(react@18.3.1)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.9

  react-remove-scroll@2.7.1(@types/react@19.1.9)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.9)(react@18.3.1)
      react-style-singleton: 2.2.3(@types/react@19.1.9)(react@18.3.1)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.9)(react@18.3.1)
      use-sidecar: 1.1.3(@types/react@19.1.9)(react@18.3.1)
    optionalDependencies:
      '@types/react': 19.1.9

  react-resizable@3.0.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      prop-types: 15.8.1
      react: 18.3.1
      react-draggable: 4.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    transitivePeerDependencies:
      - react-dom

  react-style-singleton@2.2.3(@types/react@19.1.9)(react@18.3.1):
    dependencies:
      get-nonce: 1.0.1
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.9

  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.28.2
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  redux@4.2.1:
    dependencies:
      '@babel/runtime': 7.28.2

  regexp-util@2.0.3: {}

  require-directory@2.1.1: {}

  resize-observer-polyfill@1.5.1: {}

  rollup@4.46.1:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.46.1
      '@rollup/rollup-android-arm64': 4.46.1
      '@rollup/rollup-darwin-arm64': 4.46.1
      '@rollup/rollup-darwin-x64': 4.46.1
      '@rollup/rollup-freebsd-arm64': 4.46.1
      '@rollup/rollup-freebsd-x64': 4.46.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.46.1
      '@rollup/rollup-linux-arm-musleabihf': 4.46.1
      '@rollup/rollup-linux-arm64-gnu': 4.46.1
      '@rollup/rollup-linux-arm64-musl': 4.46.1
      '@rollup/rollup-linux-loongarch64-gnu': 4.46.1
      '@rollup/rollup-linux-ppc64-gnu': 4.46.1
      '@rollup/rollup-linux-riscv64-gnu': 4.46.1
      '@rollup/rollup-linux-riscv64-musl': 4.46.1
      '@rollup/rollup-linux-s390x-gnu': 4.46.1
      '@rollup/rollup-linux-x64-gnu': 4.46.1
      '@rollup/rollup-linux-x64-musl': 4.46.1
      '@rollup/rollup-win32-arm64-msvc': 4.46.1
      '@rollup/rollup-win32-ia32-msvc': 4.46.1
      '@rollup/rollup-win32-x64-msvc': 4.46.1
      fsevents: 2.3.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  sonner@2.0.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  source-map-js@1.2.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.codepointat@0.2.1: {}

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  tailwind-merge@3.3.1: {}

  tiny-inflate@1.0.3: {}

  tiny-invariant@1.3.3: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.3)
      picomatch: 4.0.3

  trigram-utils@2.0.1:
    dependencies:
      collapse-white-space: 2.1.0
      n-gram: 2.0.2

  tslib@2.3.0: {}

  tslib@2.8.1: {}

  typescript@5.8.3: {}

  undici-types@7.8.0: {}

  unicode-regex@4.1.2:
    dependencies:
      regexp-util: 2.0.3

  unicount@1.1.0: {}

  use-callback-ref@1.3.3(@types/react@19.1.9)(react@18.3.1):
    dependencies:
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.9

  use-memo-one@1.1.3(react@18.3.1):
    dependencies:
      react: 18.3.1

  use-sidecar@1.1.3(@types/react@19.1.9)(react@18.3.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.3.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.9

  uuid@11.1.0: {}

  vite@7.0.6(@types/node@24.1.0):
    dependencies:
      esbuild: 0.25.8
      fdir: 6.4.6(picomatch@4.0.3)
      picomatch: 4.0.3
      postcss: 8.5.6
      rollup: 4.46.1
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 24.1.0
      fsevents: 2.3.3

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  y18n@5.0.8: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  zrender@5.6.1:
    dependencies:
      tslib: 2.3.0
