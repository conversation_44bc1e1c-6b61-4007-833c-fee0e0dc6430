import { UniverSheetsCorePreset } from "@univerjs/preset-sheets-core";
import UniverPresetSheetsCoreZhCN from "@univerjs/preset-sheets-core/locales/zh-CN";

import { UniverSheetsSortPreset } from "@univerjs/preset-sheets-sort";
import UniverPresetSheetsSortZhCN from "@univerjs/preset-sheets-sort/locales/zh-CN";

import { UniverSheetsFilterPreset } from "@univerjs/preset-sheets-filter";
import UniverPresetSheetsFilterZhCN from "@univerjs/preset-sheets-filter/locales/zh-CN";

import { UniverSheetsFindReplacePreset } from "@univerjs/preset-sheets-find-replace";
import UniverPresetSheetsFindReplaceZhCN from "@univerjs/preset-sheets-find-replace/locales/zh-CN";

import { UniverSheetsNotePreset } from "@univerjs/preset-sheets-note";
import UniverPresetSheetsNoteZhCN from "@univerjs/preset-sheets-note/locales/zh-CN";

import { UniverSheetsDataValidationPreset } from "@univerjs/preset-sheets-data-validation";
import UniverPresetSheetsDataValidationZhCN from "@univerjs/preset-sheets-data-validation/locales/zh-CN";
import { createUniver, LocaleType, mergeLocales } from "@univerjs/presets";
import type { Univer, FUniver } from "@univerjs/presets";
import "@univerjs/preset-sheets-core/lib/index.css";
import "@univerjs/preset-sheets-filter/lib/index.css";
import "@univerjs/preset-sheets-find-replace/lib/index.css";
import "@univerjs/preset-sheets-data-validation/lib/index.css";
import "@univerjs/preset-sheets-sort/lib/index.css";
import "@univerjs/preset-sheets-note/lib/index.css";
import MainCustomExtension from "./plugins/main.extension";

// 导入自定义样式

// 定义 Univer 实例接口
interface UniverInstance {
  univerAPI: FUniver;
  univer: Univer;
}

// 定义菜单配置接口
interface MenuConfig {
  [key: string]: {
    hidden: boolean;
  };
}

// 创建 Univer 实例的函数
function createUniverInstance(): UniverInstance {
  const { univerAPI, univer } = createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: mergeLocales(
        UniverPresetSheetsCoreZhCN,
        UniverPresetSheetsFilterZhCN,
        UniverPresetSheetsFindReplaceZhCN,
        UniverPresetSheetsDataValidationZhCN,
        UniverPresetSheetsSortZhCN,
        UniverPresetSheetsNoteZhCN
      ),
    },
    presets: [
      UniverSheetsCorePreset({
        // 禁用头部工具栏
        header: false,
        toolbar: false,
        // 禁用底部所有组件
        footer: false,
        // 保留右键菜单但限制功能
        contextMenu: true,
        // 自定义菜单配置，只保留基本的复制粘贴插入删除功能
        menu: {
          "sheet.contextMenu.permission": { hidden: true },
          "sheet.menu.sheet-frozen": { hidden: true },
          "sheet.menu.paste-special": { hidden: true },
          "sheet.menu.clear-selection": { hidden: true },
          "sheet.command.set-row-height": { hidden: true },
          "sheet.command.set-worksheet-col-width": { hidden: true },
          "sheet.command.insert-multi-cols-before": { hidden: true },
          "sheet.command.insert-multi-cols-right": { hidden: true },
          "sheet.command.hide-col-confirm": { hidden: true },
          "sheet.command.set-col-auto-width": { hidden: true },

          // 隐藏其他高级功能

          "sheet.menu.delete": { hidden: true },
          "sheet.menu.cell-insert": { hidden: true },
          "sheet.command.insert-multi-rows-above": { hidden: true },
          "sheet.command.insert-multi-rows-after": { hidden: true },
          "sheet.command.hide-row-confirm": { hidden: true },
          "sheet.command.set-row-is-auto-height": { hidden: true },

          // 保留基本编辑功能（这些通常默认显示，明确设置为显示）
          "sheet.command.copy": { hidden: false },
          "sheet.command.paste": { hidden: false },
          "sheet.command.cut": { hidden: true },
          "sheet.command.undo": { hidden: true },
          "sheet.command.redo": { hidden: true },

          "sheet.operation.add-note-popup": { hidden: true },
          "sheet.command.delete-note": { hidden: true },
          "sheet.command.toggle-note-popup": { hidden: true },
          "sheet.menu.sheets-sort-ctx-popup": { hidden: true },
          "sheet.menu.sheets-sort": { hidden: true },
        },
      }),
      UniverSheetsFilterPreset(),
      UniverSheetsFindReplacePreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsSortPreset(),
      UniverSheetsNotePreset(),
    ],
  });

  univerAPI.addEvent(univerAPI.Event.LifeCycleChanged, ({ stage }) => {
    console.log(stage);

    if (stage === univerAPI.Enum.LifecycleStages.Rendered) {
      const unitId = univerAPI.getActiveWorkbook()?.getId()
      if (!unitId) {
        return
      }

      // univerAPI.registerSheetRowHeaderExtension(unitId, new RowHeaderCustomExtension())
      univerAPI.registerSheetMainExtension(unitId, new MainCustomExtension())
      // univerAPI.registerSheetColumnHeaderExtension(unitId, new ColumnHeaderCustomExtension())
    }
  })

  return { univerAPI, univer };
}

// 扩展 Window 接口以包含我们的函数
declare global {
  interface Window {
    createUniver: typeof createUniverInstance;
  }
}

// 将函数挂载到 window 对象，保持向后兼容
window.createUniver = createUniverInstance;

// 导出给 IIFE 格式使用
export { createUniverInstance as createUniver };

// 创建 Univer 实例并初始化工作簿
const { univerAPI } = createUniverInstance();
univerAPI.createWorkbook({});
